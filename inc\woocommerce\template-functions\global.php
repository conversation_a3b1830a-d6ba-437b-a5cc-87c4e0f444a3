<?php
/**
 * Global WooCommerce Template Functions.
 *
 * This file contains global utility functions used across the WooCommerce implementation.
 * 
 * @package demir
 * 
 * Function Index:
 * - demir_woo_cart_available() - Validates WooCommerce cart availability
 * - demir_content_filter() - Default content filter
 * - demir_before_content() - Opens main content wrappers
 * - demir_after_content() - Closes main content wrappers
 * - demir_is_product_archive() - Checks if current page is product archive
 * - demir_widgets_disable_block_editor() - Disables <PERSON><PERSON><PERSON> for widgets
 * - demir_remove_woo_sidebar() - Removes sidebar from WooCommerce pages
 */

declare(strict_types=1);

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Cart Availability Functions
 * --------------------------
 */

if ( ! function_exists( 'demir_woo_cart_available' ) ) {
	/**
	 * Validates whether the WooCommerce Cart instance is available in the request.
	 *
	 * @since 2.6.0
	 * @return bool True if cart is available, false otherwise.
	 */
	function demir_woo_cart_available(): bool {
		$woo = WC();
		return $woo instanceof \WooCommerce && $woo->cart instanceof \WC_Cart;
	}
}

/**
 * Content Wrapper Functions
 * ------------------------
 */

if ( ! function_exists( 'demir_content_filter' ) ) {
	/**
	 * Default content filter for processing post content.
	 *
	 * @param string $details Post content to filter.
	 * @return string Filtered post content.
	 */
	function demir_content_filter( string $details ): string {
		return $details;
	}
}

if ( ! function_exists( 'demir_before_content' ) ) {
	/**
	 * Opens the main content wrappers.
	 * Wraps all WooCommerce content in wrappers that match the theme markup.
	 *
	 * @since 1.0.0
	 * @return void
	 */
	function demir_before_content(): void {
		?>
		<div id="primary" class="content-area">
			<main id="main" class="site-main" role="main">
		<?php
	}
}

if ( ! function_exists( 'demir_after_content' ) ) {
	/**
	 * Closes the main content wrappers and adds sidebar.
	 *
	 * @since 1.0.0
	 * @return void
	 */
	function demir_after_content(): void {
		?>
			</main><!-- #main -->
		</div><!-- #primary -->
		<?php
		do_action( 'demir_sidebar' );
	}
}

/**
 * Page Type Detection Functions
 * ---------------------------
 */

if ( ! function_exists( 'demir_is_product_archive' ) ) {
	/**
	 * Checks if the current page is a product archive.
	 * Includes shop, product taxonomy, category and tag pages.
	 *
	 * @since 2.0.0
	 * @return boolean True if product archive page, false otherwise.
	 */
	function demir_is_product_archive(): bool {
		if ( ! demir_is_woocommerce_activated() ) {
			return false;
		}
		
		return is_shop() || is_product_taxonomy() || is_product_category() || is_product_tag();
	}
}

/**
 * Widget Functions
 * ---------------
 */

// Get widget block editor setting
$demir_widgets_disable_block_editor = demir_get_option( 'demir_widgets_disable_block_editor' );

if ( true === $demir_widgets_disable_block_editor ) {
	/**
	 * Disables the Gutenberg block editor for widgets.
	 *
	 * @since 2.0.0
	 * @return void
	 */
	function demir_widgets_disable_block_editor(): void {
		remove_theme_support( 'widgets-block-editor' );
	}
	add_action( 'after_setup_theme', 'demir_widgets_disable_block_editor' );
}

/**
 * Sidebar Functions
 * ----------------
 */

if ( ! function_exists( 'demir_remove_woo_sidebar' ) ) {
	/**
	 * Removes sidebar from WooCommerce pages (cart, checkout, my account).
	 *
	 * @since 2.0.0
	 * @return void
	 */
	function demir_remove_woo_sidebar(): void {
		if ( is_cart() || is_checkout() || is_account_page() ) {
			remove_action( 'demir_page_sidebar', 'demir_pages_sidebar', 10 );
		}
	}
}
add_action( 'wp', 'demir_remove_woo_sidebar', 20 );


