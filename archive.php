<?php
/**
 * The template for displaying archive pages.
 *
 * Learn more: https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package demir
 */

$demir_layout_archives_sidebar = '';
$demir_layout_archives_sidebar = demir_get_option( 'demir_layout_archives_sidebar' );

$demir_layout_blog = '';
$demir_layout_blog = demir_get_option( 'demir_layout_blog' );

get_header(); ?>

	<div id="primary" class="content-area">
		<main id="main" class="site-main <?php echo demir_safe_html( $demir_layout_blog ); ?>">

		<?php if ( have_posts() ) : ?>

			<header class="page-header">
				<?php
					the_archive_title( '<h1 class="page-title">', '</h1>' );
					the_archive_description( '<div class="taxonomy-description">', '</div>' );
				?>
			</header><!-- .page-header -->

			<?php
			get_template_part( 'loop' );

		else :

			get_template_part( 'content', 'none' );

		endif;
		?>

		</main><!-- #main -->
	</div><!-- #primary -->

	<?php if ( 'no-archives-sidebar' !== $demir_layout_archives_sidebar ) { ?>
	<div id="secondary" class="widget-area" role="complementary">
		<?php dynamic_sidebar( 'sidebar-posts' ); ?>
	</div><!-- #secondary -->
	<?php } ?>

<?php
get_footer();

