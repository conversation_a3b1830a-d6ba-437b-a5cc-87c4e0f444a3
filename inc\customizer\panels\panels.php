<?php
/**
 *
 * Kirki options panels
 *
 * @package CommerceGurus
 * @subpackage demir
 */
function demir_kirki_panels( $wp_customize ) {

	$wp_customize->add_panel( 'demir_panel_general', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'General', 'demir' ),
		'description'	 => esc_html__( 'Manage general theme settings', 'demir' ),
	) );
	$wp_customize->add_panel( 'demir_panel_colors', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Colors', 'demir' ),
		'description'	 => esc_html__( 'Manage theme colors', 'demir' ),
	) );
	$wp_customize->add_panel( 'demir_panel_mainmenu', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Header and Navigation', 'demir' ),
		'description'	 => esc_html__( 'Manage the header and navigation', 'demir' ),
	) );
	$wp_customize->add_panel( 'demir_panel_heading', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Page Heading', 'demir' ),
		'description'	 => esc_html__( 'Manage the page heading', 'demir' ),
	) );
	$wp_customize->add_panel( 'demir_panel_typography', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Typography', 'demir' ),
		'description'	 => esc_html__( 'Manage theme typography', 'demir' ),
	) );
	$wp_customize->add_panel( 'demir_panel_layout', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Layout', 'demir' ),
		'description'	 => esc_html__( 'Manage theme header, footer and more', 'demir' ),
	) );
	$wp_customize->add_panel( 'demir_panel_blog', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Blog', 'demir' ),
		'description'	 => esc_html__( 'Manage blog settings', 'demir' ),
	) );
}

add_action( 'customize_register', 'demir_kirki_panels' );


