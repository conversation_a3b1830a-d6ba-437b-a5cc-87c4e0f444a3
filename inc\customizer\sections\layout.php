<?php
/**
 *
 * Kirki layout section
 *
 * @package CommerceGurus
 * @subpackage demir
 */
function demir_kirki_section_layout( $wp_customize ) {

	$wp_customize->add_section( 'demir_layout_section_general', array(
		'title'			 => esc_html__( 'General', 'demir' ),
		'panel'			 => 'demir_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_layout_section_sidebars', array(
		'title'			 => esc_html__( 'Sidebars', 'demir' ),
		'panel'			 => 'demir_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_layout_section_blog', array(
		'title'			 => esc_html__( 'Blog', 'demir' ),
		'panel'			 => 'demir_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_layout_section_woocommerce', array(
		'title'			 => esc_html__( 'WooCommerce', 'demir' ),
		'description'	 => esc_html__( 'Publish and refresh to see the changes.', 'demir' ),
		'panel'			 => 'demir_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_layout_section_footer', array(
		'title'			 => esc_html__( 'Footer', 'demir' ),
		'panel'			 => 'demir_panel_layout',
		'priority'		 => 10,
	) );
}

add_action( 'customize_register', 'demir_kirki_section_layout' );


