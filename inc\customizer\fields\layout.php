<?php
/**
 *
 * Layout theme options
 *
 * @package CommerceGurus
 * @subpackage demir
 */

// Layout fields.
$demir_default_options = demir_get_option_defaults();

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_general_heading_1',
		'section'  => 'demir_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Wrapper', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_layout_wrapper',
		'label'    => esc_attr__( 'Contain the grid?', 'demir' ),
		'section'  => 'demir_layout_section_general',
		'default'  => 'no',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'demir' ),
			'no'  => esc_attr__( 'No', 'demir' ),

		),
		'priority' => 10,
	)
);

// Wrapper width.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_wrapper_width_nb',
		'label'       => esc_html__( 'Wraper container width', 'demir' ),
		'description' => esc_html__( 'Adjust wrapper width in px.', 'demir' ),
		'section'     => 'demir_layout_section_general',
		'default'     => 2170,
		'priority'    => 10,
		'choices'     => array(
			'min'  => 992,
			'max'  => 3000,
			'step' => 1,
		),
		'active_callback'    => array(
			array(
				'setting'  => 'demir_layout_wrapper',
				'value'    => 'yes',
				'operator' => '==',
			),
		),
		'output'      => array(
			array(
				'element'  => '#page',
				'property' => 'max-width',
				'units'    => 'px',
			),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_general_heading_2',
		'section'  => 'demir_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Content container', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Content Container width.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_container_width',
		'label'       => esc_html__( 'Content container width', 'demir' ),
		'description' => esc_html__( 'Adjust the width of your content container in pixels. Default is 1170px.', 'demir' ),
		'section'     => 'demir_layout_section_general',
		'default'     => 1170,
		'priority'    => 10,
		'choices'     => array(
			'min'  => 992,
			'max'  => 2000,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.col-full, .single-product .site-content .demir-sticky-add-to-cart .col-full, body .woocommerce-message, .single-product .site-content .commercekit-sticky-add-to-cart .col-full, .wc-block-components-notice-banner',
				'property' => 'max-width',
				'units'    => 'px',
			),
			array(
				'element'  => 'body.header-4:not(.full-width-header) .header-4-inner,
				.summary form.cart.commercekit_sticky-atc .commercekit-pdp-sticky-inner,
				.commercekit-atc-sticky-tabs ul.commercekit-atc-tab-links,
				.h-ckit-filters.no-woocommerce-sidebar .commercekit-product-filters',
				'property' => 'max-width',
				'units'    => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'       => '
			.product-details-wrapper,
			.single-product .woocommerce:has(.woocommerce-message),
			.single-product .woocommerce-Tabs-panel,
			.single-product .archive-header .woocommerce-breadcrumb,
			 .plp-below.archive.woocommerce .archive-header .woocommerce-breadcrumb,
			.related.products,
			.site-content #sspotReviews:not([data-shortcode="1"]),
			.upsells.products,
			.composite_summary,
			.composite_wrap,
			.wc-prl-recommendations,
			.yith-wfbt-section.woocommerce',
				'value_pattern' => 'calc($px + 5.2325em)',
				'property'      => 'max-width',
				'units'         => '',
			),
			array(
				'element'       => '.main-navigation ul li.menu-item-has-children.full-width .container,
				.single-product .woocommerce-error',
				'property'      => 'max-width',
				'units'         => 'px',
			),
			array(
				'element'       => '.below-content .col-full, footer .col-full',
				'value_pattern' => 'calc($px + 40px)',
				'property'      => 'max-width',
				'units'         => '',
			),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_general_heading_3',
		'section'  => 'demir_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Breadcrumbs', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Display Breadcrumbs.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_display_breadcrumbs',
		'label'     => esc_html__( 'Display breadcrumbs', 'demir' ),
		'section'   => 'demir_layout_section_general',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Breadcrumbs type.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_layout_woocommerce_breadcrumbs_type',
		'label'    => esc_attr__( 'Breadcrumbs type', 'demir' ),
		'section'  => 'demir_layout_section_general',
		'default'  => $demir_default_options['demir_layout_woocommerce_breadcrumbs_type'],
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_display_breadcrumbs',
				'value'    => true,
				'operator' => '==',
			),
		),
		'choices'  => array(
			'default' 		=> esc_attr__( 'Default', 'demir' ),
			'aioseo'  		=> esc_attr__( 'AIOSEO', 'demir' ),
			'rankmath'  	=> esc_attr__( 'Rank Math', 'demir' ),
			'seoframework'  => esc_attr__( 'SEO Framework', 'demir' ),
			'seopress'  	=> esc_attr__( 'SEOPress', 'demir' ),
			'yoast'  		=> esc_attr__( 'Yoast', 'demir' ),
		),
		'priority' => 10,
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_general_heading_4',
		'section'  => 'demir_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Mobile product grid', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_layout_woocommerce_mobile_grid',
		'label'    => esc_attr__( 'Products per row on mobile', 'demir' ),
		'section'  => 'demir_layout_section_general',
		'default'  => 'mobile-grid-two',
		'choices'  => array(
			'mobile-grid-one' => esc_attr__( 'One per row', 'demir' ),
			'mobile-grid-two'  => esc_attr__( 'Two per row', 'demir' ),
		),
		'priority' => 10,
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_general_heading_5',
		'section'  => 'demir_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Widgets', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Disable block editor for widgets.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_widgets_disable_block_editor',
		'label'     => esc_html__( 'Disable block editor for widgets', 'demir' ),
		'section'   => 'demir_layout_section_general',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_sidebar_heading_1',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'General', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Enable sidebar cart drawer.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_enable_sidebar_cart',
		'label'     => esc_html__( 'Enable sidebar cart drawer', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Enable single product ajax add to cart.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_single_product_ajax',
		'label'     => esc_html__( 'Enable single product ajax', 'demir' ),
		'description' => esc_html__( 'Add directly to the cart on single products', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_sidebar_heading_2',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Shop', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Display Shop title.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_shop_title',
		'label'     => esc_html__( 'Display shop heading', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Display Products Results Count.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_display_count',
		'label'     => esc_html__( 'Display product results count', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display Products Sorting.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_display_sorting',
		'label'     => esc_html__( 'Display product sorting', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display sale flash over image.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'toggle',
		'settings' => 'demir_layout_woocommerce_display_badge',
		'label'    => esc_html__( 'Display sale % discount', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => 1,
		'priority' => 10,
	)
);

// Sale badge type.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_layout_woocommerce_display_badge_type',
		'label'    => esc_attr__( 'Sale badge design', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => $demir_default_options['demir_layout_woocommerce_display_badge_type'],
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_display_badge',
				'value'    => 1,
				'operator' => '==',
			),
		),
		'choices'  => array(
			'default' => esc_attr__( 'Circle', 'demir' ),
			'bubble'  => esc_attr__( 'Bubble', 'demir' ),
		),
		'priority' => 10,
	)
);

// Display rating.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_display_rating',
		'label'     => esc_html__( 'Display rating', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display rating count.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_catalog_reviews_count',
		'label'     => esc_html__( 'Display rating count', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 0,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_display_rating',
				'value'    => 1,
				'operator' => '==',
			),
		),
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display category.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_display_category',
		'label'     => esc_html__( 'Display category', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display image change on hover.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_flip_image',
		'label'     => esc_html__( 'Image change on hover', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Product card display.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_woocommerce_card_display',
		'label'     => esc_html__( 'Product card display', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_woocommerce_card_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'default'   => esc_html__( 'Default', 'demir' ),
			'slide' => esc_html__( 'Slide up', 'demir' ),
		),
	)
);

// CTA button display.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_woocommerce_cta_display',
		'label'     => esc_html__( 'Button display', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_woocommerce_cta_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'hover'   => esc_html__( 'On hover (desktop only)', 'demir' ),
			'static' => esc_html__( 'Static', 'demir' ),
			'no-cta' => esc_html__( 'Remove buttons', 'demir' ),
		),
	)
);

// Text alignment.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_woocommerce_text_alignment',
		'label'     => esc_html__( 'Product text alignment', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_woocommerce_text_alignment'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'product-align-left'   => esc_html__( 'Left', 'demir' ),
			'product-align-center' => esc_html__( 'Center', 'demir' ),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_sidebar_heading_3',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Product Categories', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Display subcategories.
/*demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_display_subcategories',
		'label'     => esc_html__( 'Display subcategories', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);
*/

// Category description layout.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_woocommerce_category_position',
		'label'     => esc_html__( 'Category description layout', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_woocommerce_category_position'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'below-header'   => esc_html__( 'Below header', 'demir' ),
			'within-content' => esc_html__( 'Within content', 'demir' ),
		),
	)
);

// Category description display.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_category_description',
		'label'     => esc_html__( 'Display category description', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_category_position',
				'value'    => 'within-content',
				'operator' => '==',
			),
		),
	)
);

// Category image display.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_category_image',
		'label'     => esc_html__( 'Display category image', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_category_position',
				'value'    => 'within-content',
				'operator' => '==',
			),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_sidebar_heading_4',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Single Product', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Enable block editor for PDPs.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_pdp_block_editor',
		'label'     => esc_html__( 'Enable block editor', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => '1',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Product gallery width.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_pdp_gallery_width',
		'label'     => esc_html__( 'Product gallery width', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_pdp_gallery_width'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'skinny'   	=> esc_html__( 'Skinny', 'demir' ),
			'regular' 	=> esc_html__( 'Regular', 'demir' ),
			'wide' 		=> esc_html__( 'Wide (default)', 'demir' ),
		),
	)
);

// PDP short description position.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_pdp_short_description_position',
		'label'     => esc_html__( 'Product short description position', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_pdp_short_description_position'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'top'   => esc_html__( 'Top', 'demir' ),
			'bottom' 	=> esc_html__( 'Bottom', 'demir' ),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_1',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display sticky add to cart bar.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_sticky_cart_display',
		'label'     => esc_html__( 'Legacy sticky add to cart bar', 'demir' ),
		'description' => esc_html__( 'We recommend adding a sticky add to cart bar via CommerceKit', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Sticky add to cart bar position.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_layout_woocommerce_sticky_cart_position',
		'label'    => esc_attr__( 'Sticky add to cart bar position', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => $demir_default_options['demir_layout_woocommerce_sticky_cart_position'],
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_sticky_cart_display',
				'value'    => true,
				'operator' => '==',
			),
		),
		'choices'  => array(
			'top' => esc_attr__( 'Top', 'demir' ),
			'bottom'  => esc_attr__( 'Bottom', 'demir' ),

		),
		'priority' => 10,
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_2',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display product meta data.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_meta_display',
		'label'     => esc_html__( 'Display product meta data', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display previous/next products.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_prev_next_display',
		'label'     => esc_html__( 'Display previous/next', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => '1',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_3',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display PDP cross-sells carousel.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_cross_sells_carousel',
		'label'     => esc_html__( 'Display cross-sells carousel', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => '0',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// PDP cross-sells carousel heading.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'text',
		'settings'  => 'demir_cross_sells_carousel_heading',
		'label'     => esc_html__( 'Cross-sells carousel heading:', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_cross_sells_carousel_heading'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => array(
			array(
				'setting'  => 'demir_cross_sells_carousel',
				'value'    => true,
				'operator' => '==',
			),
		),
		'js_vars'   => array(
			array(
				'element'  => '.cross-sells-heading',
				'function' => 'html',
			),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_4',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Product description container width.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_pdp_description_width',
		'label'     => esc_html__( 'Product description container width', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_pdp_description_width'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'full-width'   => esc_html__( 'Full width', 'demir' ),
			'contained' => esc_html__( 'Contained', 'demir' ),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_5',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display floating button.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'toggle',
		'settings' => 'demir_layout_floating_button_display',
		'label'    => esc_attr__( 'Display floating button', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'   => '1',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Floating button text.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'text',
		'settings'  => 'demir_layout_floating_button_text',
		'label'     => esc_html__( 'Floating button title:', 'demir' ),
		'description' => esc_html__( 'Content is added within the widget: "Floating Button Modal Content"', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_layout_floating_button_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_floating_button_display',
				'value'    => true,
				'operator' => '==',
			),
		),
		'js_vars'   => array(
			array(
				'element'  => '.call-back-feature',
				'function' => 'html',
			),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_6',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display related.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_related_display',
		'label'     => esc_html__( 'Display related', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Number of related items.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'number',
		'settings' => 'demir_layout_related_amount',
		'label'    => esc_attr__( 'Number of related items to show', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'   => '4',
		'choices' => array(
		'min' => 0,
		'max' => 6,
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_7',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display upsells before related.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_upsells_first',
		'label'     => esc_html__( 'Display upsells before related', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Upsells title.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'text',
		'settings'  => 'demir_upsells_title_text',
		'label'     => esc_html__( 'Upsells title', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_upsells_title_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'js_vars'   => array(
			array(
				'element'  => '.upsells-title',
				'function' => 'html',
			),
		),
	)
);

// Number of upsells.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'number',
		'settings' => 'demir_layout_upsells_amount',
		'label'    => esc_attr__( 'Number of upsells to show', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'   => '4',
		'choices' => array(
		'min' => 1,
		'max' => 6,
		),
	)
);


demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_sidebar_heading_5',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase; letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Cart and Checkout', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Display progress bar.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'toggle',
		'settings' => 'demir_layout_progress_bar_display',
		'label'    => esc_attr__( 'Display progress bar', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => 1,
		'priority' => 10,
	)
);

// Display cross-sells.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_display_cross_sells',
		'label'     => esc_html__( 'Display cross-sells', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Number of cross sells.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'number',
		'settings' => 'demir_layout_cross_sells_amount',
		'label'    => esc_attr__( 'Number of cross-sells to show', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'   => '4',
		'active_callback'  => array(
			array(
				'setting'  => 'demir_display_cross_sells',
				'value'    => true,
				'operator' => '==',
			),
		),
		'choices' => array(
		'min' => 1,
		'max' => 6,
		),
	)
);

// Mobile Cart page layout.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        	=> 'toggle',
		'settings'    	=> 'demir_layout_woocommerce_mobile_cart_page',
		'label'       	=> esc_attr__( 'Mobile-optimized cart page', 'demir' ),
		'description' 	=> esc_attr__( 'Collapses the cart table on mobile', 'demir' ),
		'section'     	=> 'demir_layout_section_woocommerce',
		'default'   	=> $demir_default_options['demir_layout_woocommerce_mobile_cart_page'],
		'priority'    	=> 10,
	)
);

// Ajax update cart page quantity.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'toggle',
		'settings' => 'demir_ajaxcart_quantity',
		'label'    => esc_attr__( 'Ajax update cart page quantity', 'demir' ),
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => 0,
		'priority' => 10,
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_woocommerce_hr_rule_8',
		'section'  => 'demir_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Distration free checkout.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'toggle',
		'settings'    => 'demir_layout_woocommerce_simple_checkout',
		'label'       => esc_attr__( 'Distraction-free checkout', 'demir' ),
		'description' => esc_attr__( 'Simplifies the checkout experience for better conversions.', 'demir' ),
		'section'     => 'demir_layout_section_woocommerce',
		'default'     => 1,
		'priority'    => 10,
	)
);

// Checkout coupon code position.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_checkout_coupon_position',
		'label'     => esc_html__( 'Checkout coupon code position', 'demir' ),
		'section'   => 'demir_layout_section_woocommerce',
		'default'   => $demir_default_options['demir_checkout_coupon_position'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'top'   => esc_html__( 'Top', 'demir' ),
			'bottom' => esc_html__( 'Bottom', 'demir' ),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_sidebars_heading_0',
		'section'  => 'demir_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'WooCommerce', 'demir' ) . '</div>',
		'priority' => 10,
	)
);


// WooCommerce Sidebar.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_woocommerce_sidebar',
		'label'     => esc_html__( 'WooCommerce Sidebar', 'demir' ),
		'section'   => 'demir_layout_section_sidebars',
		'default'   => $demir_default_options['demir_layout_woocommerce_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-woocommerce-sidebar'  => esc_html__( 'Left', 'demir' ),
			'right-woocommerce-sidebar' => esc_html__( 'Right', 'demir' ),
			'no-woocommerce-sidebar'    => esc_html__( 'None', 'demir' ),
		),
	)
);

// WooCommerce Product Category Widget Toggle.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_wc_product_category_widget_toggle',
		'label'     => esc_html__( 'Product Category Widget Toggle', 'demir' ),
		'description' => esc_html__( 'Include expand/collapse buttons to the core WooCommerce product category widget.', 'demir' ),
		'section'   => 'demir_layout_section_sidebars',
		'default'   => $demir_default_options['demir_wc_product_category_widget_toggle'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'demir' ),
			'disable'  => esc_attr__( 'Disable', 'demir' ),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_sidebars_heading_1',
		'section'  => 'demir_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Pages', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Pages Sidebar.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_page_sidebar',
		'label'     => esc_html__( 'Page Sidebar', 'demir' ),
		'section'   => 'demir_layout_section_sidebars',
		'default'   => $demir_default_options['demir_layout_page_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-page-sidebar'  => esc_html__( 'Left', 'demir' ),
			'right-page-sidebar' => esc_html__( 'Right', 'demir' ),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_sidebars_heading_2',
		'section'  => 'demir_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Blog Archives', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Blog Archives Sidebar.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_archives_sidebar',
		'label'     => esc_html__( 'Blog Archives Sidebar', 'demir' ),
		'section'   => 'demir_layout_section_sidebars',
		'default'   => $demir_default_options['demir_layout_archives_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-archives-sidebar'  => esc_html__( 'Left', 'demir' ),
			'right-archives-sidebar' => esc_html__( 'Right', 'demir' ),
			'no-archives-sidebar'    => esc_html__( 'None', 'demir' ),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_sidebars_heading_3',
		'section'  => 'demir_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Single Post', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Posts Sidebar.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_post_sidebar',
		'label'     => esc_html__( 'Post Sidebar', 'demir' ),
		'section'   => 'demir_layout_section_sidebars',
		'default'   => $demir_default_options['demir_layout_post_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-post-sidebar'  => esc_html__( 'Left', 'demir' ),
			'right-post-sidebar' => esc_html__( 'Right', 'demir' ),
			'no-post-sidebar'    => esc_html__( 'None', 'demir' ),
		),
	)
);

// Sidebar Width.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_layout_sidebar_width',
		'label'       => esc_html__( 'Sidebar Width (%).', 'demir' ),
		'description' => esc_html__( 'Adjust the width of the sidebar.', 'demir' ),
		'section'     => 'demir_layout_section_sidebars',
		'default'     => 17,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 50,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  		=> '#secondary',
				'property' 		=> 'width',
				'units'    		=> '%',
				'media_query' 	=> '@media (min-width: 993px)',
			),
		),
	)
);

// Content Width.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_layout_content_width',
		'label'       => esc_html__( 'Content Width (%).', 'demir' ),
		'description' => esc_html__( 'Adjust the width of the content.', 'demir' ),
		'section'     => 'demir_layout_section_sidebars',
		'default'     => 76,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  		=> '.content-area',
				'property' 		=> 'width',
				'units'    		=> '%',
				'media_query' 	=> '@media (min-width: 993px)',
			),
		),
	)
);


// Blog Layout.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_blog',
		'label'     => esc_html__( 'Blog Layout', 'demir' ),
		'section'   => 'demir_layout_section_blog',
		'default'   => $demir_default_options['demir_layout_blog'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'list'        => esc_html__( 'List', 'demir' ),
			'flow'        => esc_html__( 'Flow', 'demir' ),
			'grid grid-2' => esc_html__( 'Grid of 2', 'demir' ),
			'grid grid-3' => esc_html__( 'Grid of 3', 'demir' ),
		),
	)
);

// Display blog page title.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_blog_title',
		'label'     => esc_html__( 'Display blog page title', 'demir' ),
		'section'   => 'demir_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display blog summary.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_blog_summary_display',
		'label'     => esc_html__( 'Display blog post summary', 'demir' ),
		'section'   => 'demir_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_layout_blog_heading_0',
		'section'  => 'demir_layout_section_blog',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Single Posts', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Display blog author.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_blog_author',
		'label'     => esc_html__( 'Display blog author', 'demir' ),
		'section'   => 'demir_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display blog meta.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_blog_meta',
		'label'     => esc_html__( 'Display blog meta', 'demir' ),
		'section'   => 'demir_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display blog previous and next.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_blog_prev_next',
		'label'     => esc_html__( 'Display blog previous/next', 'demir' ),
		'section'   => 'demir_layout_section_blog',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display single post featured image.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_post_featured_image',
		'label'     => esc_html__( 'Display featured image', 'demir' ),
		'section'   => 'demir_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Single Post Layout.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_layout_singlepost',
		'label'       => esc_html__( 'Single Post Layout.', 'demir' ),
		'description' => esc_html__( 'Layout Two is full width and better with the Block Editor.', 'demir' ),
		'section'     => 'demir_layout_section_blog',
		'default'     => $demir_default_options['demir_layout_singlepost'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'singlepost-layout-one' => esc_html__( 'Layout One', 'demir' ),
			'singlepost-layout-two' => esc_html__( 'Layout Two', 'demir' ),
		),
	)
);


// Footer fields.
// Display Below Content Area.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_below_content_display',
		'label'     => esc_html__( 'Show Below Content?', 'demir' ),
		'section'   => 'demir_layout_section_footer',
		'default'   => $demir_default_options['demir_below_content_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'show' => esc_html__( 'Show', 'demir' ),
			'hide' => esc_html__( 'Hide', 'demir' ),
		),
	)
);

// Display Footer.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_footer_display',
		'label'     => esc_html__( 'Show Footer?', 'demir' ),
		'section'   => 'demir_layout_section_footer',
		'default'   => $demir_default_options['demir_footer_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'show' => esc_html__( 'Show', 'demir' ),
			'hide' => esc_html__( 'Hide', 'demir' ),
		),
	)
);

// Display Copyright.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_copyright_display',
		'label'     => esc_html__( 'Show Copyright?', 'demir' ),
		'section'   => 'demir_layout_section_footer',
		'default'   => $demir_default_options['demir_copyright_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'show' => esc_html__( 'Show', 'demir' ),
			'hide' => esc_html__( 'Hide', 'demir' ),
		),
	)
);



