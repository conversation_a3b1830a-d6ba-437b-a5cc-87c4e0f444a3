<?php
/**
 *
 * Kirki menu section
 *
 * @package CommerceGurus
 * @subpackage demir
 */
function demir_kirki_section_mainmenu( $wp_customize ) {

	// Top Bar.
	$wp_customize->add_section( 'demir_header_section_top_bar', array(
		'title'			 => esc_html__( 'Top Bar', 'demir' ),
		'panel'			 => 'demir_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Header Layout.
	$wp_customize->add_section( 'demir_header_section_layout', array(
		'title'			 => esc_html__( 'Header', 'demir' ),
		'panel'			 => 'demir_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Mobile Header
	$wp_customize->add_section(
	'demir_section_general_mobile_header', array(
		'title'    => esc_html__( 'Mobile Header', 'demir' ),
		'panel'    => 'demir_panel_mainmenu',
		'priority' => 10,
	)
	);

	// Navigation.
	$wp_customize->add_section( 'demir_navigation_section_layout', array(
		'title'			 => esc_html__( 'Navigation', 'demir' ),
		'panel'			 => 'demir_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Cart.
	$wp_customize->add_section( 'demir_cart_section_layout', array(
		'title'			 => esc_html__( 'Cart', 'demir' ),
		'panel'			 => 'demir_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Below Header.
	$wp_customize->add_section( 'demir_below_header_section_layout', array(
		'title'			 => esc_html__( 'Below Header', 'demir' ),
		'panel'			 => 'demir_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Responsive Breakpoint.
	$wp_customize->add_section( 'demir_mainmenu_section_responsive_breakpoint', array(
		'title'			 => esc_html__( 'Responsive Breakpoint', 'demir' ),
		'panel'			 => 'demir_panel_mainmenu',
		'priority'		 => 10,
	) );
}

add_action( 'customize_register', 'demir_kirki_section_mainmenu' );


