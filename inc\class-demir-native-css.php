<?php
/**
 * Demir Native CSS Generator
 * 
 * Handles CSS output generation for customizer fields
 * Replaces Kirki CSS functionality
 *
 * @package CommerceGurus
 * @subpackage demir
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Demir Native CSS Generator Class
 */
class Demir_Native_CSS {

	/**
	 * CSS output storage
	 * @var array
	 */
	private static $css_output = array();

	/**
	 * Initialize CSS generation
	 */
	public static function init() {
		add_action( 'wp_enqueue_scripts', array( __CLASS__, 'enqueue_dynamic_css' ), 20 );
		add_action( 'wp_head', array( __CLASS__, 'print_inline_css' ), 999 );
	}

	/**
	 * Generate CSS from all registered fields
	 *
	 * @return string Generated CSS
	 */
	public static function generate_css() {
		$css = '';
		
		// Get all fields from demir_Kirki
		$fields = demir_Kirki::get_all_fields();
		
		if ( empty( $fields ) ) {
			return $css;
		}

		$css_array = array();

		foreach ( $fields as $field_id => $field ) {
			if ( ! isset( $field['output'] ) || empty( $field['output'] ) ) {
				continue;
			}

			$field_css = self::process_field( $field );
			if ( ! empty( $field_css ) ) {
				$css_array = array_merge_recursive( $css_array, $field_css );
			}
		}

		// Convert CSS array to string
		$css = self::array_to_css( $css_array );

		return $css;
	}

	/**
	 * Process individual field and generate CSS
	 *
	 * @param array $field Field configuration
	 * @return array CSS array
	 */
	private static function process_field( $field ) {
		$css_array = array();
		$field_value = get_theme_mod( $field['settings'] );

		// Skip if no value
		if ( empty( $field_value ) && $field_value !== '0' ) {
			return $css_array;
		}

		foreach ( $field['output'] as $output ) {
			$output = wp_parse_args( $output, array(
				'element'       => '',
				'property'      => '',
				'media_query'   => 'global',
				'prefix'        => '',
				'suffix'        => '',
				'units'         => '',
				'value_pattern' => '$',
				'choice'        => '',
			) );

			if ( empty( $output['element'] ) || empty( $output['property'] ) ) {
				continue;
			}

			// Process element selector
			$selector = is_array( $output['element'] ) ? implode( ', ', $output['element'] ) : $output['element'];
			
			// Process value based on field type
			$processed_value = self::process_value( $field_value, $output, $field['type'] );

			if ( $processed_value !== '' ) {
				$css_array[ $output['media_query'] ][ $selector ][ $output['property'] ] = $processed_value;
			}
		}

		return $css_array;
	}

	/**
	 * Process field value based on type and output configuration
	 *
	 * @param mixed $value Field value
	 * @param array $output Output configuration
	 * @param string $field_type Field type
	 * @return string Processed value
	 */
	private static function process_value( $value, $output, $field_type ) {
		$processed_value = '';

		switch ( $field_type ) {
			case 'typography':
				$processed_value = self::process_typography_value( $value, $output );
				break;

			case 'spacing':
				$processed_value = self::process_spacing_value( $value, $output );
				break;

			case 'multicolor':
				if ( ! empty( $output['choice'] ) && isset( $value[ $output['choice'] ] ) ) {
					$processed_value = $value[ $output['choice'] ];
				}
				break;

			case 'background':
				$processed_value = self::process_background_value( $value, $output );
				break;

			default:
				// Simple value processing
				if ( is_array( $value ) ) {
					$value = '';
				}
				$processed_value = (string) $value;
				break;
		}

		// Apply value pattern
		if ( ! empty( $output['value_pattern'] ) && $processed_value !== '' ) {
			$processed_value = str_replace( '$', $processed_value, $output['value_pattern'] );
		}

		// Add prefix, suffix, and units
		if ( $processed_value !== '' ) {
			$processed_value = $output['prefix'] . $processed_value . $output['units'] . $output['suffix'];
		}

		return $processed_value;
	}

	/**
	 * Process typography field value
	 *
	 * @param mixed $value Field value
	 * @param array $output Output configuration
	 * @return string Processed value
	 */
	private static function process_typography_value( $value, $output ) {
		if ( ! is_array( $value ) ) {
			return '';
		}

		$property = $output['property'];
		
		if ( ! isset( $value[ $property ] ) ) {
			return '';
		}

		$property_value = $value[ $property ];

		// Special handling for specific typography properties
		switch ( $property ) {
			case 'font-family':
				// Add quotes if font family contains spaces
				if ( strpos( $property_value, ' ' ) !== false && strpos( $property_value, '"' ) === false ) {
					$property_value = '"' . $property_value . '"';
				}
				break;

			case 'variant':
				// Convert variant to font-weight and font-style
				$font_weight = str_replace( 'italic', '', $property_value );
				$font_weight = ( in_array( $font_weight, array( '', 'regular' ) ) ) ? '400' : $font_weight;
				
				if ( $output['property'] === 'font-weight' ) {
					$property_value = $font_weight;
				} elseif ( $output['property'] === 'font-style' ) {
					$property_value = ( strpos( $property_value, 'italic' ) !== false ) ? 'italic' : 'normal';
				}
				break;
		}

		return $property_value;
	}

	/**
	 * Process spacing field value
	 *
	 * @param mixed $value Field value
	 * @param array $output Output configuration
	 * @return string Processed value
	 */
	private static function process_spacing_value( $value, $output ) {
		if ( ! is_array( $value ) ) {
			return '';
		}

		$property = $output['property'];
		
		// Handle spacing properties like margin-top, padding-left, etc.
		foreach ( $value as $side => $side_value ) {
			if ( empty( $property ) ) {
				$final_property = $side;
			} elseif ( strpos( $property, '%%' ) !== false ) {
				$final_property = str_replace( '%%', $side, $property );
			} else {
				$final_property = $property . '-' . $side;
			}

			if ( $output['property'] === $final_property ) {
				return $side_value;
			}
		}

		return '';
	}

	/**
	 * Process background field value
	 *
	 * @param mixed $value Field value
	 * @param array $output Output configuration
	 * @return string Processed value
	 */
	private static function process_background_value( $value, $output ) {
		if ( ! is_array( $value ) ) {
			return '';
		}

		$property = str_replace( 'background-', '', $output['property'] );
		
		if ( isset( $value[ $property ] ) ) {
			$property_value = $value[ $property ];

			// Special handling for background-image
			if ( $property === 'image' && ! empty( $property_value ) ) {
				if ( strpos( $property_value, 'url(' ) === false ) {
					$property_value = 'url("' . esc_url( $property_value ) . '")';
				}
			}

			return $property_value;
		}

		return '';
	}

	/**
	 * Convert CSS array to CSS string
	 *
	 * @param array $css_array CSS array
	 * @return string CSS string
	 */
	private static function array_to_css( $css_array ) {
		$css = '';

		foreach ( $css_array as $media_query => $selectors ) {
			// Add media query wrapper if not global
			if ( $media_query !== 'global' ) {
				$css .= $media_query . ' {';
			}

			foreach ( $selectors as $selector => $properties ) {
				$css .= $selector . ' {';

				foreach ( $properties as $property => $value ) {
					if ( ! empty( $value ) ) {
						$css .= $property . ': ' . esc_attr( $value ) . ';';
					}
				}

				$css .= '}';
			}

			// Close media query wrapper
			if ( $media_query !== 'global' ) {
				$css .= '}';
			}
		}

		return $css;
	}

	/**
	 * Enqueue dynamic CSS
	 */
	public static function enqueue_dynamic_css() {
		// Generate CSS
		$css = self::generate_css();

		if ( ! empty( $css ) ) {
			// Add inline CSS to existing stylesheet
			wp_add_inline_style( 'demir-dynamic-style', $css );
		}
	}

	/**
	 * Print inline CSS in head
	 */
	public static function print_inline_css() {
		// This is a fallback if the main stylesheet isn't enqueued
		$css = self::generate_css();

		if ( ! empty( $css ) ) {
			echo '<style id="demir-native-css">' . $css . '</style>';
		}
	}
}

// Initialize the CSS generator
Demir_Native_CSS::init();
