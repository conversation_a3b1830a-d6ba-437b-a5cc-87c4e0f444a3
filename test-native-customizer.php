<?php
/**
 * Test file for Demir Native Customizer Framework
 * 
 * This file tests the native customizer functionality
 * Run this by visiting: yoursite.com/wp-content/themes/demir/test-native-customizer.php
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Demir Native Customizer Framework Test</h1>';

// Test 1: Check if demir_Kirki class exists
echo '<h2>Test 1: Class Existence</h2>';
if (class_exists('demir_Kirki')) {
    echo '<p style="color: green;">✓ demir_Kirki class exists</p>';
} else {
    echo '<p style="color: red;">✗ demir_Kirki class not found</p>';
}

// Test 2: Check if Kirki plugin is active
echo '<h2>Test 2: Kirki Plugin Status</h2>';
if (class_exists('Kirki')) {
    echo '<p style="color: orange;">⚠ Kirki plugin is active - native framework will defer to Kirki</p>';
} else {
    echo '<p style="color: green;">✓ Kirki plugin is not active - using native framework</p>';
}

// Test 3: Check registered fields
echo '<h2>Test 3: Registered Fields</h2>';
$fields = demir_Kirki::get_all_fields();
echo '<p>Total registered fields: <strong>' . count($fields) . '</strong></p>';

if (!empty($fields)) {
    echo '<h3>Sample Fields:</h3>';
    $sample_count = 0;
    foreach ($fields as $field_id => $field) {
        if ($sample_count >= 5) break;
        echo '<div style="border: 1px solid #ccc; padding: 10px; margin: 5px 0;">';
        echo '<strong>ID:</strong> ' . esc_html($field['settings']) . '<br>';
        echo '<strong>Type:</strong> ' . esc_html($field['type']) . '<br>';
        echo '<strong>Section:</strong> ' . esc_html($field['section']) . '<br>';
        if (isset($field['label'])) {
            echo '<strong>Label:</strong> ' . esc_html($field['label']) . '<br>';
        }
        echo '</div>';
        $sample_count++;
    }
}

// Test 4: Check registered panels
echo '<h2>Test 4: Registered Panels</h2>';
$panels = demir_Kirki::get_all_panels();
echo '<p>Total registered panels: <strong>' . count($panels) . '</strong></p>';

// Test 5: Check registered sections
echo '<h2>Test 5: Registered Sections</h2>';
$sections = demir_Kirki::get_all_sections();
echo '<p>Total registered sections: <strong>' . count($sections) . '</strong></p>';

// Test 6: Test CSS generation
echo '<h2>Test 6: CSS Generation</h2>';
if (class_exists('Demir_Native_CSS')) {
    echo '<p style="color: green;">✓ Demir_Native_CSS class exists</p>';
    
    // Try to generate CSS
    $css = Demir_Native_CSS::generate_css();
    if (!empty($css)) {
        echo '<p style="color: green;">✓ CSS generation successful</p>';
        echo '<p>Generated CSS length: <strong>' . strlen($css) . '</strong> characters</p>';
        
        // Show first 500 characters of CSS
        echo '<h3>CSS Preview (first 500 chars):</h3>';
        echo '<pre style="background: #f5f5f5; padding: 10px; overflow: auto; max-height: 200px;">';
        echo esc_html(substr($css, 0, 500));
        if (strlen($css) > 500) {
            echo '...';
        }
        echo '</pre>';
    } else {
        echo '<p style="color: orange;">⚠ CSS generation returned empty result</p>';
    }
} else {
    echo '<p style="color: red;">✗ Demir_Native_CSS class not found</p>';
}

// Test 7: Test theme mod values
echo '<h2>Test 7: Theme Mod Values</h2>';
$test_settings = array(
    'demir_color_general_swatch',
    'demir_color_general_links',
    'demir_logo_height',
    'demir_typography_h1_fontfamily'
);

foreach ($test_settings as $setting) {
    $value = get_theme_mod($setting);
    echo '<p><strong>' . esc_html($setting) . ':</strong> ';
    if (!empty($value)) {
        if (is_array($value)) {
            echo '<code>' . esc_html(json_encode($value)) . '</code>';
        } else {
            echo '<code>' . esc_html($value) . '</code>';
        }
    } else {
        echo '<em>Not set</em>';
    }
    echo '</p>';
}

// Test 8: JavaScript files
echo '<h2>Test 8: JavaScript Files</h2>';
$js_files = array(
    'customizer-controls.js' => get_template_directory() . '/assets/js/customizer-controls.js',
    'customizer-preview.js' => get_template_directory() . '/assets/js/customizer-preview.js'
);

foreach ($js_files as $name => $path) {
    if (file_exists($path)) {
        echo '<p style="color: green;">✓ ' . esc_html($name) . ' exists</p>';
    } else {
        echo '<p style="color: red;">✗ ' . esc_html($name) . ' not found</p>';
    }
}

// Test 9: Check if customizer hooks are registered
echo '<h2>Test 9: WordPress Hooks</h2>';
$hooks_to_check = array(
    'customize_register',
    'wp_enqueue_scripts',
    'customize_controls_enqueue_scripts',
    'customize_preview_init'
);

foreach ($hooks_to_check as $hook) {
    $callbacks = $GLOBALS['wp_filter'][$hook] ?? null;
    if ($callbacks) {
        $demir_callbacks = 0;
        foreach ($callbacks->callbacks as $priority => $callback_group) {
            foreach ($callback_group as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'demir_Kirki') {
                    $demir_callbacks++;
                }
            }
        }
        if ($demir_callbacks > 0) {
            echo '<p style="color: green;">✓ ' . esc_html($hook) . ' has ' . $demir_callbacks . ' demir_Kirki callback(s)</p>';
        } else {
            echo '<p style="color: orange;">⚠ ' . esc_html($hook) . ' has no demir_Kirki callbacks</p>';
        }
    } else {
        echo '<p style="color: red;">✗ ' . esc_html($hook) . ' hook not found</p>';
    }
}

echo '<hr>';
echo '<p><strong>Test completed!</strong> Visit <a href="' . admin_url('customize.php') . '">WordPress Customizer</a> to see the native framework in action.</p>';
echo '<p><em>Note: Delete this test file after testing is complete.</em></p>';
?>
